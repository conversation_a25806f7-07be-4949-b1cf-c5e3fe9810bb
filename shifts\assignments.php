<?php
session_start();

// Include permissions system
require_once __DIR__ . '/../includes/permissions.php';
require_once __DIR__ . '/../includes/attendance_helpers.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require shift viewing permissions
requirePermission($mysqli, 'shifts.view');

$msg = "";
$error = "";

// Handle POST actions (for removing assignments)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action']) && $_POST['action'] === 'remove_assignment') {
    if (!hasPermission($mysqli, $_SESSION['user_id'], 'shifts.edit')) {
      $error = "You don't have permission to remove shift assignments.";
    } else {
      $assignment_id = intval($_POST['assignment_id'] ?? 0);
      if ($assignment_id) {
        $stmt = $mysqli->prepare("DELETE FROM employee_shifts WHERE id = ?");
        $stmt->bind_param('i', $assignment_id);
        if ($stmt->execute()) {
          $msg = "Shift assignment removed successfully.";
        } else {
          $error = "Failed to remove shift assignment.";
        }
        $stmt->close();
      }
    }
  }
}

// Get filter parameters
$employee_filter = $_GET['employee_id'] ?? ''; // Keep for context-aware navigation from employee edit page
$date_filter = $_GET['date'] ?? '';
$template_filter = $_GET['template'] ?? '';
$search_filter = $_GET['search'] ?? '';

// Get pagination parameters
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = intval($_GET['per_page'] ?? 25);
$per_page = in_array($per_page, [10, 25, 50, 100]) ? $per_page : 25;

// Get sorting parameters
$sort_by = $_GET['sort_by'] ?? 'date';
$sort_order = $_GET['sort_order'] ?? 'desc';
$sort_by = in_array($sort_by, ['employee', 'date']) ? $sort_by : 'date';
$sort_order = in_array($sort_order, ['asc', 'desc']) ? $sort_order : 'desc';

// Build query with filters
$where_conditions = [];
$params = [];
$param_types = '';

if ($employee_filter) {
  $where_conditions[] = "e.id = ?";
  $params[] = intval($employee_filter);
  $param_types .= 'i';
}

if ($date_filter) {
  $where_conditions[] = "? BETWEEN es.shift_start_date AND es.shift_end_date";
  $params[] = $date_filter;
  $param_types .= 's';
}

if ($template_filter) {
  $where_conditions[] = "st.id = ?";
  $params[] = intval($template_filter);
  $param_types .= 'i';
}

if ($search_filter) {
  $where_conditions[] = "(e.name LIKE ? OR st.name LIKE ? OR st.description LIKE ?)";
  $search_term = '%' . $search_filter . '%';
  $params[] = $search_term;
  $params[] = $search_term;
  $params[] = $search_term;
  $param_types .= 'sss';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Build ORDER BY clause
$order_clause = '';
if ($sort_by === 'employee') {
  $order_clause = "ORDER BY e.name " . strtoupper($sort_order) . ", es.shift_start_date DESC";
} else {
  $order_clause = "ORDER BY es.shift_start_date " . strtoupper($sort_order) . ", e.name ASC";
}

// First, get total count for pagination
$count_query = "
  SELECT COUNT(*) as total
  FROM employee_shifts es
  JOIN employees e ON es.employee_id = e.id
  JOIN shift_templates st ON es.shift_template_id = st.id
  $where_clause
";

if (!empty($params)) {
  $stmt = $mysqli->prepare($count_query);
  $stmt->bind_param($param_types, ...$params);
  $stmt->execute();
  $count_result = $stmt->get_result();
  $total_records = $count_result->fetch_assoc()['total'];
  $stmt->close();
} else {
  $count_result = $mysqli->query($count_query);
  $total_records = $count_result->fetch_assoc()['total'];
}

// Calculate pagination
$total_pages = ceil($total_records / $per_page);
$offset = ($page - 1) * $per_page;

// Fetch shift assignments with employee and template details
$assignments_query = "
  SELECT
    es.id,
    es.shift_start_date,
    es.shift_end_date,
    e.name as employee_name,
    e.id as employee_id,
    st.id as template_id,
    st.name as template_name,
    st.start_time,
    st.end_time,
    st.description
  FROM employee_shifts es
  JOIN employees e ON es.employee_id = e.id
  JOIN shift_templates st ON es.shift_template_id = st.id
  $where_clause
  $order_clause
  LIMIT $per_page OFFSET $offset
";

if (!empty($params)) {
  $stmt = $mysqli->prepare($assignments_query);
  $stmt->bind_param($param_types, ...$params);
  $stmt->execute();
  $assignments_result = $stmt->get_result();
  $assignments = $assignments_result->fetch_all(MYSQLI_ASSOC);
  $stmt->close();
} else {
  $assignments_result = $mysqli->query($assignments_query);
  $assignments = $assignments_result->fetch_all(MYSQLI_ASSOC);
}

/**
 * Calculate total break duration for a shift template
 */
function calculateTotalBreakDuration($mysqli, $template_id) {
  $total_break_minutes = 0;

  $stmt = $mysqli->prepare("SELECT break_start, break_end FROM shift_breaks WHERE shift_template_id = ?");
  $stmt->bind_param("i", $template_id);
  $stmt->execute();
  $stmt->bind_result($break_start, $break_end);

  while ($stmt->fetch()) {
    $start = new DateTime($break_start);
    $end = new DateTime($break_end);

    // Handle breaks that span midnight (though unlikely for breaks)
    if ($end < $start) {
      $end->add(new DateInterval('P1D'));
    }

    $break_duration = $start->diff($end);
    $total_break_minutes += ($break_duration->h * 60) + $break_duration->i;
  }

  $stmt->close();
  return $total_break_minutes;
}

// Fetch employees for filter dropdown
$employees_result = $mysqli->query("SELECT id, name FROM employees ORDER BY name");
$employees = $employees_result->fetch_all(MYSQLI_ASSOC);

// Fetch shift templates for filter dropdown
$templates_result = $mysqli->query("SELECT id, name FROM shift_templates ORDER BY name");
$templates = $templates_result->fetch_all(MYSQLI_ASSOC);

// Check user permissions for UI
$can_edit = hasPermission($mysqli, $_SESSION['user_id'], 'shifts.edit');
$can_assign = hasPermission($mysqli, $_SESSION['user_id'], 'shifts.assign');

// Set global variable for navigation to display total count
$GLOBALS['page_total_count'] = $total_records;

// Helper function to build URLs with current parameters
function buildUrl($new_params = []) {
  global $employee_filter, $date_filter, $template_filter, $search_filter, $per_page, $sort_by, $sort_order;

  $params = [
    'employee_id' => $employee_filter,
    'date' => $date_filter,
    'template' => $template_filter,
    'search' => $search_filter,
    'per_page' => $per_page,
    'sort_by' => $sort_by,
    'sort_order' => $sort_order,
    'page' => 1
  ];

  // Override with new parameters
  $params = array_merge($params, $new_params);

  // Remove empty parameters
  $params = array_filter($params, function($value) {
    return $value !== '' && $value !== null;
  });

  return 'assignments.php?' . http_build_query($params);
}

// Helper function to get sort icon
function getSortIcon($column) {
  global $sort_by, $sort_order;
  if ($sort_by !== $column) {
    return '<i class="bi bi-arrow-down-up text-muted"></i>';
  }
  return $sort_order === 'asc'
    ? '<i class="bi bi-arrow-up text-primary"></i>'
    : '<i class="bi bi-arrow-down text-primary"></i>';
}

// Helper function to get next sort order
function getNextSortOrder($column) {
  global $sort_by, $sort_order;
  if ($sort_by !== $column) {
    return 'asc';
  }
  return $sort_order === 'asc' ? 'desc' : 'asc';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Shift Assignments - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
</head>
<body>
  <?php include '../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">
    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- Streamlined Filters & Controls -->
    <div class="content-card">
      <!-- Responsive Filter Bar -->
      <form method="GET" class="filter-bar">
        <!-- Desktop Layout (lg and up) -->
        <div class="row g-2 align-items-end d-none d-lg-flex">
          <!-- Search -->
          <div class="col-lg-5">
            <label for="search" class="form-label-sm">Search</label>
            <input type="text" class="form-control filter-input" id="search" name="search"
                   placeholder="🔍 Employee name, template..." value="<?= htmlspecialchars($search_filter) ?>" />
          </div>

          <!-- Date -->
          <div class="col-lg-2">
            <label for="date" class="form-label-sm">Date</label>
            <input type="date" class="form-control filter-input" id="date" name="date"
                   value="<?= htmlspecialchars($date_filter) ?>" />
          </div>

          <!-- Template -->
          <div class="col-lg-2">
            <label for="template" class="form-label-sm">Template</label>
            <select class="form-select filter-select" id="template" name="template">
              <option value="">All</option>
              <?php foreach ($templates as $template): ?>
                <option value="<?= $template['id'] ?>" <?= $template_filter == $template['id'] ? 'selected' : '' ?>>
                  <?= htmlspecialchars($template['name']) ?>
                </option>
              <?php endforeach; ?>
            </select>
          </div>

          <!-- Show -->
          <div class="col-lg-1">
            <label for="per_page" class="form-label-sm">Show</label>
            <select class="form-select filter-select" id="per_page" name="per_page" onchange="this.form.submit()">
              <option value="10" <?= $per_page == 10 ? 'selected' : '' ?>>10</option>
              <option value="25" <?= $per_page == 25 ? 'selected' : '' ?>>25</option>
              <option value="50" <?= $per_page == 50 ? 'selected' : '' ?>>50</option>
              <option value="100" <?= $per_page == 100 ? 'selected' : '' ?>>100</option>
            </select>
          </div>

          <!-- Actions -->
          <div class="col-lg-2">
            <div class="d-flex gap-1 filter-actions">
              <button type="submit" class="btn btn-primary filter-btn flex-fill">
                <i class="bi bi-funnel me-1"></i>
                Filter
              </button>
              <a href="assignments.php" class="btn btn-outline-secondary filter-btn" title="Clear filters">
                <i class="bi bi-x-lg"></i>
              </a>
            </div>
          </div>
        </div>

        <!-- Mobile Ultra Compact Layout (md and below) -->
        <div class="d-lg-none">
          <!-- Row 1: Search -->
          <div class="row g-1 mb-2">
            <div class="col-12">
              <label for="search_mobile" class="form-label-sm">Search</label>
              <input type="text" class="form-control filter-input-sm" id="search_mobile" name="search"
                     placeholder="🔍 Employee name, template..." value="<?= htmlspecialchars($search_filter) ?>" />
            </div>
          </div>

          <!-- Row 2: Template -->
          <div class="row g-1 mb-2">
            <div class="col-12">
              <label for="template_mobile" class="form-label-sm">Template</label>
              <select class="form-select filter-select-sm" id="template_mobile" name="template">
                <option value="">All Templates</option>
                <?php foreach ($templates as $template): ?>
                  <option value="<?= $template['id'] ?>" <?= $template_filter == $template['id'] ? 'selected' : '' ?>>
                    <?= htmlspecialchars($template['name']) ?>
                  </option>
                <?php endforeach; ?>
              </select>
            </div>
          </div>

          <!-- Row 3: Date and Show side by side (50-50) -->
          <div class="row g-1 mb-2">
            <div class="col-6">
              <label for="date_mobile" class="form-label-sm">Date</label>
              <input type="date" class="form-control filter-input-sm" id="date_mobile" name="date"
                     value="<?= htmlspecialchars($date_filter) ?>" />
            </div>
            <div class="col-6">
              <label for="per_page_mobile" class="form-label-sm">Show</label>
              <select class="form-select filter-select-sm" id="per_page_mobile" name="per_page" onchange="this.form.submit()">
                <option value="10" <?= $per_page == 10 ? 'selected' : '' ?>>10</option>
                <option value="25" <?= $per_page == 25 ? 'selected' : '' ?>>25</option>
                <option value="50" <?= $per_page == 50 ? 'selected' : '' ?>>50</option>
                <option value="100" <?= $per_page == 100 ? 'selected' : '' ?>>100</option>
              </select>
            </div>
          </div>

          <!-- Row 4: Actions -->
          <div class="row g-1">
            <div class="col-12">
              <div class="d-flex gap-1">
                <button type="submit" class="btn btn-primary btn-sm flex-fill">
                  <i class="bi bi-funnel me-1"></i>
                  Filter
                </button>
                <a href="assignments.php" class="btn btn-outline-secondary btn-sm" title="Clear">
                  <i class="bi bi-x-lg"></i>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Hidden fields to preserve sorting -->
        <input type="hidden" name="sort_by" value="<?= htmlspecialchars($sort_by) ?>" />
        <input type="hidden" name="sort_order" value="<?= htmlspecialchars($sort_order) ?>" />
        <input type="hidden" name="page" value="1" />
      </form>
    </div>

    <!-- Enhanced Assignments Table -->
    <div class="content-card mt-2">
      <?php if (empty($assignments)): ?>
        <div class="text-center py-5">
          <i class="bi bi-calendar-x display-1 text-muted"></i>
          <h4 class="mt-3 text-muted">No Shift Assignments Found</h4>
          <p class="text-muted">
            <?php if ($employee_filter || $date_filter || $template_filter || $search_filter): ?>
              Try adjusting your filters or
              <a href="assignments.php" class="text-decoration-none">clear all filters</a>.
            <?php else: ?>
              Start by <a href="assign.php" class="text-decoration-none">assigning shifts to employees</a>.
            <?php endif; ?>
          </p>
        </div>
      <?php else: ?>
        <!-- Results Summary -->
        <div class="d-flex justify-content-between align-items-center mb-3">
          <div class="text-muted">
            Showing <?= number_format(($page - 1) * $per_page + 1) ?> to
            <?= number_format(min($page * $per_page, $total_records)) ?> of
            <?= number_format($total_records) ?> assignments
          </div>
          <div class="text-muted small">
            Page <?= $page ?> of <?= $total_pages ?>
          </div>
        </div>

        <!-- Desktop Table -->
        <div class="table-container enhanced-table d-none d-lg-block">
          <table class="table table-hover mb-0">
            <thead>
              <tr>
                <th class="sortable" onclick="window.location='<?= buildUrl(['sort_by' => 'employee', 'sort_order' => getNextSortOrder('employee')]) ?>'">
                  <div class="d-flex align-items-center justify-content-between">
                    <span>Employee</span>
                    <?= getSortIcon('employee') ?>
                  </div>
                </th>
                <th class="sortable" onclick="window.location='<?= buildUrl(['sort_by' => 'date', 'sort_order' => getNextSortOrder('date')]) ?>'">
                  <div class="d-flex align-items-center justify-content-between">
                    <span>Date</span>
                    <?= getSortIcon('date') ?>
                  </div>
                </th>
                <th>Shift Template</th>
                <th>Time</th>
                <th>Duration</th>
                <?php if ($can_edit): ?>
                <th class="text-center">Actions</th>
                <?php endif; ?>
              </tr>
            </thead>
            <tbody>
            <?php foreach ($assignments as $assignment): ?>
              <tr>
                <td>
                  <strong><?= htmlspecialchars($assignment['employee_name']) ?></strong>
                </td>
                <td>
                  <?php if ($assignment['shift_start_date'] === $assignment['shift_end_date']): ?>
                    <span class="badge bg-primary">
                      <?= date('M j, Y', strtotime($assignment['shift_start_date'])) ?>
                    </span>
                  <?php else: ?>
                    <span class="badge bg-info">
                      <?= date('M j', strtotime($assignment['shift_start_date'])) ?> -
                      <?= date('M j, Y', strtotime($assignment['shift_end_date'])) ?>
                    </span>
                  <?php endif; ?>
                </td>
                <td><?= htmlspecialchars($assignment['template_name']) ?></td>
                <td>
                  <?= date('g:i A', strtotime($assignment['start_time'])) ?> -
                  <?= date('g:i A', strtotime($assignment['end_time'])) ?>
                </td>
                <td>
                  <?php
                  $start = new DateTime($assignment['start_time']);
                  $end = new DateTime($assignment['end_time']);
                  if ($end < $start) $end->add(new DateInterval('P1D')); // Next day
                  $duration = $start->diff($end);

                  // Calculate total shift duration in minutes
                  $total_shift_minutes = ($duration->h * 60) + $duration->i;

                  // Get total break duration for this shift template
                  $break_minutes = calculateTotalBreakDuration($mysqli, $assignment['template_id']);

                  // Calculate actual working duration (shift duration - break duration)
                  $working_minutes = max(0, $total_shift_minutes - $break_minutes);
                  $working_hours = floor($working_minutes / 60);
                  $working_mins = $working_minutes % 60;

                  echo $working_hours . ' hours ' . $working_mins . ' minutes';
                  ?>
                </td>
                <?php if ($can_edit): ?>
                <td>
                  <form method="POST" style="display: inline;"
                        onsubmit="return confirm('Are you sure you want to remove this shift assignment?')">
                    <input type="hidden" name="action" value="remove_assignment" />
                    <input type="hidden" name="assignment_id" value="<?= $assignment['id'] ?>" />
                    <button type="submit" class="btn btn-outline-danger btn-sm">
                      <i class="bi bi-trash"></i>
                    </button>
                  </form>
                </td>
                <?php endif; ?>
              </tr>
            <?php endforeach; ?>
            </tbody>
          </table>
        </div>

        <!-- Mobile Cards -->
        <div class="mobile-assignment-cards d-lg-none">
          <?php foreach ($assignments as $assignment): ?>
            <?php
            // Calculate duration for display
            $start = new DateTime($assignment['start_time']);
            $end = new DateTime($assignment['end_time']);
            if ($end < $start) $end->add(new DateInterval('P1D'));
            $duration = $start->diff($end);
            $total_shift_minutes = ($duration->h * 60) + $duration->i;
            $break_minutes = calculateTotalBreakDuration($mysqli, $assignment['template_id']);
            $working_minutes = max(0, $total_shift_minutes - $break_minutes);
            $working_hours = floor($working_minutes / 60);
            $working_mins = $working_minutes % 60;
            ?>
            <div class="mobile-assignment-card"
                 data-employee="<?= strtolower(htmlspecialchars($assignment['employee_name'])) ?>"
                 data-template="<?= strtolower(htmlspecialchars($assignment['template_name'])) ?>">
              <div class="card-header" onclick="toggleMobileCard(this)">
                <!-- Main Info Row -->
                <div class="d-flex align-items-center justify-content-between mb-2">
                  <div class="d-flex align-items-center gap-3">
                    <div class="avatar">
                      <?= strtoupper(substr($assignment['employee_name'], 0, 1)) ?>
                    </div>
                    <div class="flex-grow-1">
                      <div class="fw-bold text-dark mb-1"><?= htmlspecialchars($assignment['employee_name']) ?></div>
                      <div class="d-flex align-items-center gap-2">
                        <span class="shift-template-badge">
                          <i class="bi bi-briefcase me-1"></i>
                          <?= htmlspecialchars($assignment['template_name']) ?>
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="text-end">
                    <?php if ($assignment['shift_start_date'] === $assignment['shift_end_date']): ?>
                      <div class="date-badge bg-primary">
                        <?= date('M j', strtotime($assignment['shift_start_date'])) ?>
                      </div>
                    <?php else: ?>
                      <div class="date-badge bg-info">
                        <?= date('M j', strtotime($assignment['shift_start_date'])) ?> -
                        <?= date('M j', strtotime($assignment['shift_end_date'])) ?>
                      </div>
                    <?php endif; ?>
                    <i class="bi bi-chevron-down expand-icon mt-1"></i>
                  </div>
                </div>

                <!-- Quick Info Row -->
                <div class="quick-info-row">
                  <div class="info-item">
                    <i class="bi bi-clock text-primary"></i>
                    <span class="info-text">
                      <?= date('g:i A', strtotime($assignment['start_time'])) ?> -
                      <?= date('g:i A', strtotime($assignment['end_time'])) ?>
                    </span>
                  </div>
                  <div class="info-item">
                    <i class="bi bi-hourglass-split text-success"></i>
                    <span class="info-text"><?= $working_hours ?>h <?= $working_mins ?>m</span>
                  </div>
                </div>
              </div>

              <div class="card-details" style="display: none;">
                <div class="detail-item">
                  <small>Date:</small>
                  <strong>
                    <?php if ($assignment['shift_start_date'] === $assignment['shift_end_date']): ?>
                      <?= date('M j, Y', strtotime($assignment['shift_start_date'])) ?>
                    <?php else: ?>
                      <?= date('M j', strtotime($assignment['shift_start_date'])) ?> -
                      <?= date('M j, Y', strtotime($assignment['shift_end_date'])) ?>
                    <?php endif; ?>
                  </strong>
                </div>

                <div class="detail-item">
                  <small>Time:</small>
                  <strong>
                    <?= date('g:i A', strtotime($assignment['start_time'])) ?> -
                    <?= date('g:i A', strtotime($assignment['end_time'])) ?>
                  </strong>
                </div>

                <div class="detail-item">
                  <small>Duration:</small>
                  <strong>
                    <?php
                    // Calculate duration (same logic as desktop)
                    $start = new DateTime($assignment['start_time']);
                    $end = new DateTime($assignment['end_time']);
                    if ($end < $start) $end->add(new DateInterval('P1D'));
                    $duration = $start->diff($end);
                    $total_shift_minutes = ($duration->h * 60) + $duration->i;
                    $break_minutes = calculateTotalBreakDuration($mysqli, $assignment['template_id']);
                    $working_minutes = max(0, $total_shift_minutes - $break_minutes);
                    $working_hours = floor($working_minutes / 60);
                    $working_mins = $working_minutes % 60;

                    echo $working_hours . 'h ' . $working_mins . 'm';
                    ?>
                  </strong>
                </div>

                <?php if ($can_edit): ?>
                  <h6 class="mt-3"><i class="bi bi-gear me-2"></i>Actions</h6>
                  <div class="d-flex gap-2">
                    <form method="POST" style="flex: 1;"
                          onsubmit="return confirm('Are you sure you want to remove this shift assignment?')">
                      <input type="hidden" name="action" value="remove_assignment" />
                      <input type="hidden" name="assignment_id" value="<?= $assignment['id'] ?>" />
                      <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                        <i class="bi bi-trash me-1"></i>
                        Remove Assignment
                      </button>
                    </form>
                  </div>
                <?php endif; ?>
              </div>
            </div>
          <?php endforeach; ?>
        </div>

        <!-- Enhanced Pagination -->
        <?php if ($total_pages > 1): ?>
          <div class="d-flex justify-content-between align-items-center mt-4 pt-3 border-top">
            <div class="pagination-info text-muted small">
              <?= number_format($total_records) ?> total assignments
            </div>

            <nav aria-label="Assignments pagination">
              <ul class="pagination pagination-sm mb-0">
                <!-- Previous Page -->
                <?php if ($page > 1): ?>
                  <li class="page-item">
                    <a class="page-link" href="<?= buildUrl(['page' => $page - 1]) ?>" aria-label="Previous">
                      <i class="bi bi-chevron-left"></i>
                    </a>
                  </li>
                <?php else: ?>
                  <li class="page-item disabled">
                    <span class="page-link"><i class="bi bi-chevron-left"></i></span>
                  </li>
                <?php endif; ?>

                <!-- Page Numbers -->
                <?php
                $start_page = max(1, $page - 2);
                $end_page = min($total_pages, $page + 2);

                // Show first page if not in range
                if ($start_page > 1): ?>
                  <li class="page-item">
                    <a class="page-link" href="<?= buildUrl(['page' => 1]) ?>">1</a>
                  </li>
                  <?php if ($start_page > 2): ?>
                    <li class="page-item disabled">
                      <span class="page-link">...</span>
                    </li>
                  <?php endif; ?>
                <?php endif; ?>

                <!-- Current range -->
                <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                  <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                    <?php if ($i == $page): ?>
                      <span class="page-link"><?= $i ?></span>
                    <?php else: ?>
                      <a class="page-link" href="<?= buildUrl(['page' => $i]) ?>"><?= $i ?></a>
                    <?php endif; ?>
                  </li>
                <?php endfor; ?>

                <!-- Show last page if not in range -->
                <?php if ($end_page < $total_pages): ?>
                  <?php if ($end_page < $total_pages - 1): ?>
                    <li class="page-item disabled">
                      <span class="page-link">...</span>
                    </li>
                  <?php endif; ?>
                  <li class="page-item">
                    <a class="page-link" href="<?= buildUrl(['page' => $total_pages]) ?>"><?= $total_pages ?></a>
                  </li>
                <?php endif; ?>

                <!-- Next Page -->
                <?php if ($page < $total_pages): ?>
                  <li class="page-item">
                    <a class="page-link" href="<?= buildUrl(['page' => $page + 1]) ?>" aria-label="Next">
                      <i class="bi bi-chevron-right"></i>
                    </a>
                  </li>
                <?php else: ?>
                  <li class="page-item disabled">
                    <span class="page-link"><i class="bi bi-chevron-right"></i></span>
                  </li>
                <?php endif; ?>
              </ul>
            </nav>
          </div>
        <?php endif; ?>
      <?php endif; ?>
    </div>

    <!-- Quick Stats -->
    <div class="row">
      <div class="col-md-4">
        <div class="content-card text-center">
          <h3 class="text-primary"><?= count($assignments) ?></h3>
          <p class="text-muted mb-0">Total Assignments</p>
        </div>
      </div>
      <div class="col-md-4">
        <div class="content-card text-center">
          <h3 class="text-success"><?= count(array_unique(array_column($assignments, 'employee_id'))) ?></h3>
          <p class="text-muted mb-0">Employees with Shifts</p>
        </div>
      </div>
      <div class="col-md-4">
        <div class="content-card text-center">
          <h3 class="text-info"><?= count(array_unique(array_column($assignments, 'template_name'))) ?></h3>
          <p class="text-muted mb-0">Templates in Use</p>
        </div>
      </div>
    </div>
  </div>

  <style>
    /* Enhanced Table & Filter Styling */
    .filter-bar {
      background: rgba(102, 126, 234, 0.02);
      border: 1px solid rgba(102, 126, 234, 0.1);
      border-radius: 12px;
      padding: 1rem;
      margin-bottom: 0;
    }

    /* Compact spacing for assignments page */
    .content-card {
      margin-bottom: 0.5rem;
    }

    .content-card.mt-2 {
      margin-top: 0.5rem !important;
    }

    .form-label-sm {
      font-size: 0.75rem;
      font-weight: 600;
      color: var(--text-secondary);
      margin-bottom: 0.25rem;
      text-transform: uppercase;
      letter-spacing: 0.025em;
    }

    /* Consistent Filter Element Sizing */
    .filter-input,
    .filter-select {
      height: 38px;
      font-size: 0.875rem;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 0.5rem 0.75rem;
      transition: all 0.2s ease;
    }

    /* Mobile Compact Filter Elements */
    .filter-input-sm,
    .filter-select-sm {
      height: 32px;
      font-size: 0.8rem;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 0.375rem 0.5rem;
      transition: all 0.2s ease;
    }

    /* Ensure mobile columns don't stack when they shouldn't */
    @media (max-width: 991.98px) {
      .filter-bar .row .col-6 {
        flex: 0 0 50%;
        max-width: 50%;
      }

      .filter-bar .row .col-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
      }

      .filter-bar .row .col-2 {
        flex: 0 0 16.666667%;
        max-width: 16.666667%;
      }
    }

    .filter-input-group {
      height: 38px;
    }

    .filter-input-addon {
      height: 38px;
      border: 1px solid #dee2e6;
      border-radius: 8px 0 0 8px;
      background: #f8f9fa;
      color: #6c757d;
      font-size: 0.875rem;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 0.75rem;
    }

    .filter-input-group .filter-input {
      border-left: none;
      border-radius: 0 8px 8px 0;
    }

    .filter-btn {
      height: 38px;
      font-size: 0.875rem;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.25rem;
    }

    .filter-actions {
      height: 38px;
    }

    /* Prevent overlapping in filter elements */
    .filter-bar .row > div {
      position: relative;
      z-index: 1;
      min-width: 0; /* Prevent flex items from overflowing */
    }

    .filter-bar .input-group,
    .filter-bar .form-select,
    .filter-bar .form-control {
      width: 100%;
      box-sizing: border-box;
      min-width: 0; /* Prevent overflow */
    }

    /* Ensure proper spacing between filter elements */
    .filter-bar .row {
      margin: 0;
    }

    .filter-bar .row > div {
      padding-left: 0.375rem;
      padding-right: 0.375rem;
    }

    .enhanced-table {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }

    .enhanced-table .table thead th {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border: none;
      padding: 1rem 0.75rem;
      font-weight: 600;
      color: var(--text-primary);
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 0.025em;
      border-bottom: 2px solid var(--border-color);
    }

    .enhanced-table .table tbody td {
      padding: 0.875rem 0.75rem;
      border: none;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      vertical-align: middle;
      font-size: 0.85rem;
    }

    .enhanced-table .table tbody tr:hover {
      background: rgba(102, 126, 234, 0.03);
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      transition: all 0.2s ease;
    }

    .enhanced-table .table tbody tr:last-child td {
      border-bottom: none;
    }

    .sortable {
      cursor: pointer;
      user-select: none;
      transition: all 0.2s ease;
    }

    .sortable:hover {
      background: rgba(102, 126, 234, 0.1) !important;
      color: var(--primary-color);
    }

    .sortable i {
      font-size: 0.75rem;
      opacity: 0.7;
      transition: all 0.2s ease;
    }

    .sortable:hover i {
      opacity: 1;
    }

    /* Enhanced Pagination */
    .pagination-sm .page-link {
      padding: 0.375rem 0.75rem;
      font-size: 0.8rem;
      border-radius: 6px;
      margin: 0 2px;
      border: 1px solid var(--border-color);
      color: var(--text-secondary);
      transition: all 0.2s ease;
    }

    .pagination-sm .page-link:hover {
      background: var(--primary-gradient);
      color: white;
      border-color: transparent;
      transform: translateY(-1px);
    }

    .pagination-sm .page-item.active .page-link {
      background: var(--primary-gradient);
      border-color: transparent;
      color: white;
    }

    .pagination-sm .page-item.disabled .page-link {
      color: var(--text-muted);
      background: transparent;
      border-color: var(--border-color);
    }

    /* Compact Badge Styling */
    .badge {
      font-size: 0.7rem;
      padding: 0.25rem 0.5rem;
      border-radius: 6px;
      font-weight: 500;
    }

    /* Mobile Assignment Cards */
    .mobile-assignment-cards {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .mobile-assignment-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
      border: 1px solid #e9ecef;
      overflow: hidden;
      transition: all 0.3s ease;
      width: 100%;
      max-width: 100%;
      box-sizing: border-box;
    }

    .mobile-assignment-card:hover {
      box-shadow: 0 4px 20px rgba(0,0,0,0.12);
      transform: translateY(-2px);
      border-color: #dee2e6;
    }

    .mobile-assignment-card .card-header {
      padding: 1rem;
      cursor: pointer;
      transition: all 0.2s ease;
      border-bottom: 1px solid transparent;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
    }

    .mobile-assignment-card .card-header:hover {
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    }

    .mobile-assignment-card.expanded .card-header {
      border-bottom-color: #e9ecef;
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    }

    .mobile-assignment-card .expand-icon {
      transition: transform 0.3s ease;
      color: #6c757d;
      font-size: 1rem;
    }

    .mobile-assignment-card.expanded .expand-icon {
      transform: rotate(180deg);
      color: var(--primary-color);
    }

    /* Enhanced Card Elements */
    .mobile-assignment-card .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: var(--primary-gradient);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 700;
      font-size: 1rem;
      flex-shrink: 0;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    }

    .shift-template-badge {
      background: rgba(102, 126, 234, 0.1);
      color: var(--primary-color);
      padding: 0.25rem 0.5rem;
      border-radius: 6px;
      font-size: 0.75rem;
      font-weight: 500;
      border: 1px solid rgba(102, 126, 234, 0.2);
    }

    .date-badge {
      color: white;
      padding: 0.3rem 0.6rem;
      border-radius: 8px;
      font-size: 0.75rem;
      font-weight: 600;
      text-align: center;
      min-width: 60px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .quick-info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 0.5rem;
      border-top: 1px solid rgba(0,0,0,0.05);
      margin-top: 0.5rem;
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: 0.4rem;
      flex: 1;
    }

    .info-item i {
      font-size: 0.9rem;
      width: 16px;
      text-align: center;
    }

    .info-text {
      font-size: 0.8rem;
      font-weight: 500;
      color: #495057;
    }

    .mobile-assignment-card .card-details {
      padding: 0 0.75rem 0.75rem 0.75rem;
      border-top: 1px solid #f1f3f4;
      background-color: #fafbfc;
    }

    .mobile-assignment-card .detail-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.4rem 0;
      border-bottom: 1px solid #f1f3f4;
      font-size: 0.85rem;
    }

    .mobile-assignment-card .detail-item:last-child {
      border-bottom: none;
    }

    .mobile-assignment-card .detail-item small {
      font-weight: 500;
      min-width: 70px;
      font-size: 0.75rem;
      color: var(--text-secondary);
    }

    .mobile-assignment-card h6 {
      font-size: 0.75rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      padding-bottom: 0.3rem;
      border-bottom: 1px solid #e9ecef;
    }

    .mobile-assignment-card .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: var(--primary-gradient);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 0.9rem;
      flex-shrink: 0;
    }

    /* Mobile Responsiveness */
    @media (max-width: 1200px) {
      .filter-bar .row > div {
        margin-bottom: 0.75rem;
      }
    }

    @media (max-width: 992px) {
      .filter-bar .row {
        gap: 0.75rem;
      }

      .filter-bar .row > div {
        margin-bottom: 1rem;
      }

      .filter-bar .col-md-12 {
        flex: 0 0 100%;
        max-width: 100%;
      }

      .filter-bar .col-md-6 {
        flex: 0 0 calc(50% - 0.375rem);
        max-width: calc(50% - 0.375rem);
      }
    }

    @media (max-width: 768px) {
      .filter-bar {
        padding: 1rem 0.75rem;
      }

      .filter-bar .row {
        gap: 1rem;
      }

      /* Default: full width for most elements */
      .filter-bar .row > div {
        margin-bottom: 0;
        flex: 0 0 100%;
        max-width: 100%;
      }

      /* Exception: Date and Show should stay side by side */
      .filter-bar .row .col-6 {
        flex: 0 0 calc(50% - 0.5rem) !important;
        max-width: calc(50% - 0.5rem) !important;
      }

      /* Force Date and Show row to display as flex */
      .filter-bar .row:has(.col-6) {
        display: flex !important;
        flex-wrap: nowrap !important;
      }

      /* Alternative approach - target the specific row with Date and Show */
      .filter-bar .d-lg-none .row:nth-child(3) {
        display: flex !important;
        flex-wrap: nowrap !important;
      }

      .filter-bar .d-lg-none .row:nth-child(3) .col-6 {
        flex: 0 0 50% !important;
        max-width: 50% !important;
        margin-bottom: 0 !important;
      }

      .filter-input,
      .filter-select,
      .filter-btn {
        height: 44px;
        font-size: 0.9rem;
      }

      .filter-input-group {
        height: 44px;
      }

      .filter-input-addon {
        height: 44px;
      }

      .enhanced-table {
        font-size: 0.75rem;
      }

      .enhanced-table .table thead th,
      .enhanced-table .table tbody td {
        padding: 0.5rem 0.375rem;
      }

      .d-flex.justify-content-between {
        flex-direction: column;
        gap: 0.5rem;
      }

      .pagination {
        justify-content: center;
      }

      /* Mobile Card Responsive Adjustments */
      .mobile-assignment-card {
        margin: 0;
        border-radius: 10px;
        width: 100%;
        max-width: 100%;
      }

      .mobile-assignment-card .card-header {
        padding: 0.875rem;
        max-width: 100%;
        box-sizing: border-box;
      }

      .mobile-assignment-card .card-details {
        padding: 0 0.875rem 0.875rem 0.875rem;
        max-width: 100%;
        box-sizing: border-box;
      }

      .mobile-assignment-card .avatar {
        width: 36px;
        height: 36px;
        font-size: 0.9rem;
      }

      .shift-template-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
      }

      .date-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
        min-width: 55px;
      }

      .info-text {
        font-size: 0.75rem;
      }

      .info-item i {
        font-size: 0.8rem;
      }

      .btn-sm {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
      }
    }

    /* Loading State */
    .table-loading {
      position: relative;
    }

    .table-loading::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255,255,255,0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }
  </style>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Mobile card toggle functionality
    function toggleMobileCard(headerElement) {
      const card = headerElement.closest('.mobile-assignment-card');
      const details = card.querySelector('.card-details');
      const isExpanded = card.classList.contains('expanded');

      if (isExpanded) {
        // Collapse
        details.style.display = 'none';
        card.classList.remove('expanded');
      } else {
        // Expand
        details.style.display = 'block';
        card.classList.add('expanded');
      }
    }

    // Auto-submit form on per_page change (both desktop and mobile)
    document.querySelectorAll('[name="per_page"]').forEach(function(element) {
      element.addEventListener('change', function() {
        this.form.submit();
      });
    });

    // Enhanced search with debounce (both desktop and mobile)
    let searchTimeout;
    document.querySelectorAll('[name="search"]').forEach(function(element) {
      element.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
          // Auto-submit after 500ms of no typing
          // this.form.submit();
        }, 500);
      });
    });

    // Add loading state to table during navigation
    document.querySelectorAll('.sortable, .page-link').forEach(element => {
      element.addEventListener('click', function() {
        const tableContainer = document.querySelector('.enhanced-table');
        if (tableContainer) {
          tableContainer.classList.add('table-loading');
        }
      });
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
      // Ctrl/Cmd + K to focus search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchElement = document.querySelector('[name="search"]:not([style*="display: none"])');
        if (searchElement) {
          searchElement.focus();
        }
      }
    });

    // Initialize mobile functionality
    document.addEventListener('DOMContentLoaded', function() {
      // Add click outside to collapse mobile cards (optional UX enhancement)
      document.addEventListener('click', function(e) {
        if (window.innerWidth < 992) {
          const clickedCard = e.target.closest('.mobile-assignment-card');
          if (!clickedCard) {
            // Clicked outside any card, collapse all
            document.querySelectorAll('.mobile-assignment-card.expanded').forEach(card => {
              card.classList.remove('expanded');
              const details = card.querySelector('.card-details');
              if (details) details.style.display = 'none';
            });
          }
        }
      });
    });
  </script>
</body>
</html>

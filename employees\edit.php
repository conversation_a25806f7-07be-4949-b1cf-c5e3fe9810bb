<?php
session_start();

// Include configuration and permissions system
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/permissions.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require employee edit permissions
requirePermission($mysqli, 'employees.edit');

$msg = "";
$error = "";
$employee_id = intval($_GET['id'] ?? 0);

if (!$employee_id) {
  header('Location: ' . appUrl('employees/list.php'));
  exit;
}

// Fetch employee data
$stmt = $mysqli->prepare("SELECT * FROM employees WHERE id = ?");
$stmt->bind_param('i', $employee_id);
$stmt->execute();
$result = $stmt->get_result();
$employee = $result->fetch_assoc();
$stmt->close();

if (!$employee) {
  header('Location: ' . appUrl('employees/list.php'));
  exit;
}

// Fetch user account information
$user_stmt = $mysqli->prepare("SELECT u.id, u.username FROM users u WHERE u.employee_id = ?");
$user_stmt->bind_param('i', $employee_id);
$user_stmt->execute();
$user_result = $user_stmt->get_result();
$user_account = $user_result->fetch_assoc();
$user_stmt->close();

// Fetch user roles if user account exists
$user_roles = [];
$all_roles = [];
if ($user_account) {
  // Fetch user roles
  $user_roles_query = "
      SELECT r.id, r.name, r.display_name, r.description
      FROM roles r
      JOIN user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = ?
      ORDER BY r.display_name
  ";

  $user_roles_stmt = $mysqli->prepare($user_roles_query);
  if ($user_roles_stmt) {
      $user_roles_stmt->bind_param('i', $user_account['id']);
      if ($user_roles_stmt->execute()) {
          $user_roles_result = $user_roles_stmt->get_result();

          while ($row = $user_roles_result->fetch_assoc()) {
              // Ensure we have a proper array with all required fields
              if (is_array($row) && isset($row['id'], $row['display_name'])) {
                  $user_roles[] = $row;
              }
          }
      }
      $user_roles_stmt->close();
  }
}

// Fetch all available roles
$all_roles_stmt = $mysqli->prepare("SELECT * FROM roles ORDER BY name");
$all_roles_stmt->execute();
$all_roles = $all_roles_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
$all_roles_stmt->close();

$current_role_ids = array_column($user_roles, 'id');

// Fetch recent shift assignments
$shifts_stmt = $mysqli->prepare("
  SELECT es.*, st.name as template_name, st.start_time, st.end_time
  FROM employee_shifts es
  JOIN shift_templates st ON es.shift_template_id = st.id
  WHERE es.employee_id = ?
  ORDER BY es.shift_start_date DESC
  LIMIT 5
");
$shifts_stmt->bind_param('i', $employee_id);
$shifts_stmt->execute();
$shifts_result = $shifts_stmt->get_result();
$recent_shifts = [];
while ($row = $shifts_result->fetch_assoc()) {
  $recent_shifts[] = $row;
}
$shifts_stmt->close();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['update_employee'])) {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $department = trim($_POST['department'] ?? '');
    $position = trim($_POST['position'] ?? '');
    $hire_date = $_POST['hire_date'] ?? '';
    $status = $_POST['status'] ?? 'active';

    if ($name) {
      try {
        // Check if email is unique (excluding current employee)
        if ($email) {
          $email_check = $mysqli->prepare("SELECT id FROM employees WHERE email = ? AND id != ?");
          $email_check->bind_param('si', $email, $employee_id);
          $email_check->execute();
          $email_result = $email_check->get_result();

          if ($email_result->num_rows > 0) {
            $error = "Email address is already in use by another employee.";
          }
          $email_check->close();
        }

        if (!$error) {
          // Update employee
          $stmt = $mysqli->prepare("UPDATE employees SET name = ?, email = ?, phone = ?, department = ?, position = ?, hire_date = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
          $stmt->bind_param('sssssssi', $name, $email, $phone, $department, $position, $hire_date, $status, $employee_id);

          if ($stmt->execute()) {
            $msg = "Employee information updated successfully.";

            // Refresh employee data
            $refresh_stmt = $mysqli->prepare("SELECT * FROM employees WHERE id = ?");
            $refresh_stmt->bind_param('i', $employee_id);
            $refresh_stmt->execute();
            $refresh_result = $refresh_stmt->get_result();
            $employee = $refresh_result->fetch_assoc();
            $refresh_stmt->close();
          } else {
            $error = "Failed to update employee information.";
          }
          $stmt->close();
        }
      } catch (Exception $e) {
        $error = "Error updating employee: " . $e->getMessage();
      }
    } else {
      $error = "Employee name is required.";
    }
  }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Edit Employee - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
</head>
<body>
  <?php include '../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">
    <!-- Page Header -->
    <div class="page-header">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">Edit Employee</h1>
          <p class="page-subtitle">Update employee information and details</p>
        </div>
        <div class="d-flex gap-3">
          <?php if ($user_account && hasPermission($mysqli, $_SESSION['user_id'], 'users.edit')): ?>
            <a href="<?= appUrl('users/edit.php?id=' . $user_account['id']) ?>" class="btn btn-primary">
              <i class="bi bi-key me-2"></i>
              Edit User Account
            </a>
          <?php endif; ?>
          <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'shifts.assign')): ?>
            <a href="<?= appUrl('shifts/assign.php?employee_id=' . $employee_id) ?>" class="btn btn-secondary">
              <i class="bi bi-calendar-plus me-2"></i>
              Assign Shifts
            </a>
          <?php endif; ?>
          <a href="<?= appUrl('employees/list.php') ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to Employee List
          </a>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- Employee Metadata -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-info-circle me-2"></i>
        Employee Details
      </h2>
      <div class="row">
        <div class="col-md-3">
          <div class="d-flex align-items-center">
            <div class="avatar me-3" style="width: 60px; height: 60px; font-size: 1.5rem;">
              <?= strtoupper(substr($employee['name'], 0, 1)) ?>
            </div>
            <div>
              <h5 class="mb-1"><?= htmlspecialchars($employee['name']) ?></h5>
              <small class="text-muted">Employee ID: <?= $employee['id'] ?></small>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <small class="text-muted">Created</small>
          <div class="fw-semibold">
            <?= $employee['created_at'] ? date('M j, Y', strtotime($employee['created_at'])) : 'N/A' ?>
          </div>
        </div>
        <div class="col-md-3">
          <small class="text-muted">Last Updated</small>
          <div class="fw-semibold">
            <?= $employee['updated_at'] ? date('M j, Y g:i A', strtotime($employee['updated_at'])) : 'N/A' ?>
          </div>
        </div>
        <div class="col-md-3">
          <small class="text-muted">Current Status</small>
          <div>
            <?php
            $status_class = match($employee['status']) {
              'active' => 'bg-success',
              'inactive' => 'bg-warning',
              'terminated' => 'bg-danger',
              default => 'bg-secondary'
            };
            ?>
            <span class="badge <?= $status_class ?>">
              <?= ucfirst($employee['status']) ?>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Employee Information Card -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-person-gear me-2"></i>
        Edit Employee Information
      </h2>
      <form method="POST" id="employeeForm">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="name" class="form-label">
                <i class="bi bi-person me-2"></i>
                Full Name *
              </label>
              <input
                type="text"
                class="form-control"
                name="name"
                id="name"
                required
                placeholder="Enter employee full name"
                value="<?= htmlspecialchars($employee['name']) ?>"
              />
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="email" class="form-label">
                <i class="bi bi-envelope me-2"></i>
                Email Address
              </label>
              <input
                type="email"
                class="form-control"
                name="email"
                id="email"
                placeholder="<EMAIL>"
                value="<?= htmlspecialchars($employee['email'] ?? '') ?>"
              />
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="phone" class="form-label">
                <i class="bi bi-telephone me-2"></i>
                Phone Number
              </label>
              <input
                type="tel"
                class="form-control"
                name="phone"
                id="phone"
                placeholder="+****************"
                value="<?= htmlspecialchars($employee['phone'] ?? '') ?>"
              />
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="hire_date" class="form-label">
                <i class="bi bi-calendar me-2"></i>
                Hire Date
              </label>
              <input
                type="date"
                class="form-control"
                name="hire_date"
                id="hire_date"
                value="<?= $employee['hire_date'] ?? '' ?>"
              />
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="department" class="form-label">
                <i class="bi bi-building me-2"></i>
                Department
              </label>
              <input
                type="text"
                class="form-control"
                name="department"
                id="department"
                placeholder="e.g., Operations, HR, IT"
                value="<?= htmlspecialchars($employee['department'] ?? '') ?>"
              />
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="position" class="form-label">
                <i class="bi bi-briefcase me-2"></i>
                Job Title
              </label>
              <input
                type="text"
                class="form-control"
                name="position"
                id="position"
                placeholder="e.g., Sales Associate, Team Lead, Customer Service Rep, Accountant"
                value="<?= htmlspecialchars($employee['position'] ?? '') ?>"
              />
              <small class="form-text text-muted">
                <i class="bi bi-info-circle me-1"></i>
                The employee's specific job role or position within the company
              </small>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="status" class="form-label">
                <i class="bi bi-person-check me-2"></i>
                Employment Status
              </label>
              <select class="form-select" name="status" id="status">
                <option value="active" <?= $employee['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                <option value="inactive" <?= $employee['status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                <option value="terminated" <?= $employee['status'] === 'terminated' ? 'selected' : '' ?>>Terminated</option>
              </select>
            </div>
          </div>
        </div>

        <div class="d-flex gap-3 mt-4">
          <button type="submit" name="update_employee" class="btn btn-primary">
            <i class="bi bi-check-circle me-2"></i>
            Update Employee
          </button>
          <a href="<?= appUrl('employees/list.php') ?>" class="btn btn-secondary">
            <i class="bi bi-x-circle me-2"></i>
            Cancel
          </a>
        </div>
      </form>
    </div>

    <!-- Recent Shift Assignments -->
    <div class="content-card">
      <h3 class="card-title">
        <i class="bi bi-calendar-week me-2"></i>
        Recent Shift Assignments
      </h3>
      <?php if (!empty($recent_shifts)): ?>
        <div class="list-group list-group-flush">
          <?php foreach ($recent_shifts as $shift): ?>
            <div class="list-group-item border-0 px-0">
              <div class="d-flex justify-content-between align-items-start">
                <div>
                  <h6 class="mb-1"><?= htmlspecialchars($shift['template_name']) ?></h6>
                  <small class="text-muted">
                    <?= date('M j', strtotime($shift['shift_start_date'])) ?>
                    <?php if ($shift['shift_start_date'] !== $shift['shift_end_date']): ?>
                      - <?= date('M j, Y', strtotime($shift['shift_end_date'])) ?>
                    <?php else: ?>
                      , <?= date('Y', strtotime($shift['shift_start_date'])) ?>
                    <?php endif; ?>
                  </small>
                </div>
                <small class="text-muted">
                  <?= date('g:i A', strtotime($shift['start_time'])) ?> - <?= date('g:i A', strtotime($shift['end_time'])) ?>
                </small>
              </div>
            </div>
          <?php endforeach; ?>
        </div>
        <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'shifts.view')): ?>
          <div class="mt-3">
            <a href="<?= appUrl('shifts/assignments.php?employee=' . $employee_id) ?>" class="btn btn-outline-primary btn-sm">
              <i class="bi bi-calendar-range me-1"></i>
              View All Shifts
            </a>
          </div>
        <?php endif; ?>
      <?php else: ?>
        <div class="text-center py-3">
          <i class="bi bi-calendar-x" style="font-size: 2rem; color: var(--text-secondary);"></i>
          <p class="mt-2 text-muted">No shift assignments found</p>
          <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'shifts.assign')): ?>
            <a href="<?= appUrl('shifts/assign.php?employee_id=' . $employee_id) ?>" class="btn btn-outline-success btn-sm">
              <i class="bi bi-plus-circle me-1"></i>
              Assign Shifts
            </a>
          <?php endif; ?>
        </div>
      <?php endif; ?>
    </div>
  </div>

  <style>
    /* Compact Design Styles */
    .page-header {
      padding: 1.25rem;
      margin-bottom: 1.25rem;
    }

    .page-title {
      font-size: 1.75rem;
      margin-bottom: 0.25rem;
    }

    .page-subtitle {
      font-size: 0.95rem;
    }

    .content-card {
      padding: 1.25rem;
      margin-bottom: 1.25rem;
    }

    .card-title {
      font-size: 1.1rem;
      margin-bottom: 0.75rem;
    }

    .avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background: var(--primary-gradient);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 0.95rem;
    }

    .form-group {
      margin-bottom: 1rem;
    }

    .form-label {
      font-size: 0.875rem;
      margin-bottom: 0.375rem;
      font-weight: 500;
    }

    .form-control, .form-select {
      padding: 0.5rem 0.75rem;
      font-size: 0.875rem;
    }

    .badge {
      font-size: 0.7rem;
      padding: 0.2rem 0.4rem;
    }

    .btn {
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
    }

    .btn-sm {
      padding: 0.375rem 0.75rem;
      font-size: 0.8rem;
    }

    .form-text {
      font-size: 0.75rem;
      margin-top: 0.25rem;
    }

    .list-group-item {
      padding: 0.75rem 0;
    }

    .list-group-item h6 {
      font-size: 0.9rem;
      margin-bottom: 0.25rem;
    }

    .list-group-item small {
      font-size: 0.75rem;
    }

    /* Compact row spacing */
    .row {
      margin-bottom: 0.5rem;
    }

    .row:last-child {
      margin-bottom: 0;
    }

    /* Compact metadata display */
    .col-md-3 small {
      font-size: 0.75rem;
    }

    .col-md-3 .fw-semibold {
      font-size: 0.875rem;
    }

    /* Compact form check styles */
    .form-check {
      margin-bottom: 0.5rem;
    }

    .form-check-label {
      font-size: 0.875rem;
    }

    .form-check-label strong {
      font-size: 0.875rem;
    }

    .form-check-label small {
      font-size: 0.75rem;
    }

    @media (max-width: 768px) {
      .page-header {
        padding: 1rem;
        margin-bottom: 1rem;
      }

      .page-title {
        font-size: 1.25rem;
      }

      .content-card {
        padding: 1rem;
        margin-bottom: 1rem;
      }

      .d-flex.gap-3 {
        flex-direction: column;
        gap: 0.75rem !important;
      }

      .d-flex.gap-3 .btn {
        width: 100%;
      }

      .avatar {
        width: 32px;
        height: 32px;
        font-size: 0.85rem;
      }
    }
  </style>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Form validation
    document.getElementById('employeeForm').addEventListener('submit', function(e) {
      const name = document.getElementById('name').value.trim();

      if (!name) {
        e.preventDefault();
        alert('Employee name is required.');
        document.getElementById('name').focus();
        return false;
      }
    });

    // Auto-format phone number
    document.getElementById('phone').addEventListener('input', function(e) {
      let value = e.target.value.replace(/\D/g, '');
      if (value.length >= 10) {
        value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
      }
      e.target.value = value;
    });

    // Confirm status change to terminated
    document.getElementById('status').addEventListener('change', function(e) {
      if (e.target.value === 'terminated') {
        if (!confirm('Are you sure you want to mark this employee as terminated? This is a significant status change.')) {
          e.target.value = '<?= $employee['status'] ?>';
        }
      }
    });

    // User credentials form validation
    const userCredentialsForm = document.getElementById('userCredentialsForm');
    if (userCredentialsForm) {
      userCredentialsForm.addEventListener('submit', function(e) {
        const username = document.getElementById('username').value.trim();
        const newPassword = document.getElementById('new_password').value;
        const confirmPassword = document.getElementById('confirm_password').value;

        if (!username) {
          e.preventDefault();
          alert('Username is required.');
          document.getElementById('username').focus();
          return false;
        }

        if (newPassword && newPassword !== confirmPassword) {
          e.preventDefault();
          alert('Password confirmation does not match.');
          document.getElementById('confirm_password').focus();
          return false;
        }
      });
    }

    // Create account function
    function createAccount(employeeId) {
      // TODO: Implement create account functionality
      alert('Create account functionality will be implemented in the next update.');
    }
  </script>
</body>
</html>
